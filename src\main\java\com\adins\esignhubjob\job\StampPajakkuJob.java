package com.adins.esignhubjob.job;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.constants.HttpHeaders;
import com.adins.constants.enums.StampingDocumentType;
import com.adins.constants.enums.StampingErrorDetail;
import com.adins.constants.enums.StampingErrorLocation;
import com.adins.esignhubjob.BaseJobHandler;
import com.adins.esignhubjob.model.custom.adins.AuditDataType;
import com.adins.esignhubjob.model.custom.adins.StampingErrorDetailBean;
import com.adins.esignhubjob.model.custom.client.womf.WomfUploadDocumentRequestBean;
import com.adins.esignhubjob.model.custom.pajakku.AttachEmeteraiErrorDetail;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.MsTenantSettings;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.webservice.adins.ClientDocumentUploadRequest;
import com.adins.esignhubjob.model.webservice.adins.ClientDocumentUploadResponse;
import com.adins.esignhubjob.model.webservice.client.cfi.CfiUploadDocumentRequest;
import com.adins.esignhubjob.model.webservice.client.cfi.CfiUploadDocumentResponse;
import com.adins.esignhubjob.model.webservice.client.womf.WomfUploadDocumentRequest;
import com.adins.esignhubjob.model.webservice.pajakku.EmeteraiPajakkuLoginResponseBean;
import com.adins.esignhubjob.model.webservice.pajakku.GenerateEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.StampingEmeteraiPajakkuResponse;
import com.adins.esignhubjob.model.webservice.pajakku.UploadDocPajakkuResponseBean;
import com.adins.exceptions.Status;
import com.adins.util.FtpClient;
import com.adins.util.IOUtils;
import com.adins.util.Tools;
import com.aliyun.fc.runtime.Context;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class StampPajakkuJob extends BaseJobHandler {

	private static final String DUMMY_BASE64_CONTENT = "base64Document";
	private static final String ERR_MSG_FAIL_RESPONSE =  "Failed Response";
    private static final String SERVER = "**************";
    private static final int PORT = 21;
    private static final String USERNAME = "AdInsftp";
    private static final String PASS = "AdIns2025**";

    private static final String ERR_LOC_FINAL_VAL = "Final Validation";
    private static final String ERR_LOC_DETAIL_VALIDATION = "Failed Validation";

    @Override
    public void doHandleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {
        String input = IOUtils.toString(inputStream);
        context.getLogger().info("Stamp Pajakku/Peruri for id_document_h: " + input);
        
        Long idDocumentH = Long.valueOf(input);
        TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByIdDocumentHNewTrx(idDocumentH);
        if (null == documentH) {
            context.getLogger().error("Stamp Pajakku/Peruri: ID " + input + " not found");
            return;
        } 

        if (!"1".equals(documentH.getIsActive()) || !Short.valueOf("54").equals(documentH.getProsesMaterai())) {
            context.getLogger().error(String.format("Tenant %1$s, Kontrak %2$s flagged as failed stamp, is_active status: %3$s, proses_materai status: %4$s", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber(), documentH.getIsActive(), documentH.getProsesMaterai()));
            logicFactory.getCommonStampingLogic().updateDocumentHMeteraiProcess(documentH, "51", context);
            return;
        }

		doPajakkuStamping(documentH, context);	
    }
    
    void doPajakkuStamping(TrDocumentH documentH, Context context) {
        logicFactory.getCommonStampingLogic().updateDocumentHMeteraiProcess(documentH, "55", context);

        long connectTimeout = logicFactory.getCommonStampingLogic().getStampingConnectionTimeout(StampingDocumentType.REGULAR_DOCUMENT, context);
        long readTimeout = logicFactory.getCommonStampingLogic().getStampingReadTimeout(StampingDocumentType.REGULAR_DOCUMENT, context);

        FtpClient ftpClient = new FtpClient(SERVER, PORT, USERNAME, PASS);

        EmeteraiPajakkuLoginResponseBean loginResponse = logicFactory.getEmateraiPajakkuLogic().loginPeruri(documentH, connectTimeout, readTimeout, context);
        String loginToken = loginResponse.getToken();
        if (!Constants.PERURI_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
            return;
        }

        List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
        context.getLogger().info(String.format("Document need Stamp : %1$s", documents.size()));
        for (TrDocumentD document : documents) {
            context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Total Meterai: %3$s, Total Stamping: %4$s", documentH.getRefNumber(), document.getDocumentId(), document.getTotalMaterai(), document.getTotalStamping()));

            if (null == document.getTotalMaterai() || 0 == document.getTotalMaterai()) {
                logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_SDT_FIN, context);
                continue;
            }

            List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getNotStampedDocumentDStampdutiesNewTrx(document);
            context.getLogger().info(String.format("Stamp needed for document %1$s : %2$s", document.getDocumentId(), docSdts.size()));
            for (TrDocumentDStampduty docSdt : docSdts) {
                if (Constants.STEP_STAMPING_NOT_STR.equals(document.getSdtProcess())) {
                    logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_UPL_DOC, context);
                }

                context.getLogger().info("before upl doc if");
                if (Constants.STEP_STAMPING_UPL_DOC.equals(document.getSdtProcess())) {
                    UploadDocPajakkuResponseBean response = logicFactory.getEmateraiPajakkuLogic().uploadDocumentForStamping(document, docSdt, ftpClient, context);
                    if (!Constants.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
                        return;
                    }
                }
                context.getLogger().info("after upl doc if");


                if (Constants.STEP_STAMPING_GEN_SDT.equals(document.getSdtProcess())) {
                    GenerateEmeteraiPajakkuResponse response = logicFactory.getEmateraiPajakkuLogic().generateEmeterai(document, docSdt, loginToken, connectTimeout, readTimeout, ftpClient, context);
                    if (!Constants.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
                        return;
                    }
                }

                if (Constants.STEP_STAMPING_STM_SDT.equals(document.getSdtProcess())) {
                    StampingEmeteraiPajakkuResponse response = logicFactory.getEmateraiPajakkuLogic().stampEmeterai(document, docSdt, loginToken, connectTimeout, readTimeout, ftpClient, context);
                    if (!Constants.PERURI_STAMPING_SUCCESS_STATUS.equalsIgnoreCase(response.getStatus())) {
                        return;
                    }
                }

                if (Constants.STEP_STAMPING_UPL_OSS.equals(document.getSdtProcess())) {
                    AttachEmeteraiErrorDetail response = logicFactory.getEmateraiPajakkuLogic().storeStampedDocumentToOss(document, ftpClient, context);
                    if (!Constants.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
                        return;
                    }
                }

                context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Total stamped: %3$s/%4$s", documentH.getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai()));
            }

            if (Constants.STEP_STAMPING_UPL_OSS.equals(document.getSdtProcess())) {
                AttachEmeteraiErrorDetail response = logicFactory.getEmateraiPajakkuLogic().storeStampedDocumentToOss(document, ftpClient, context);
                if (!Constants.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
                    return;
                }
            }

            if (Constants.STEP_STAMPING_UPL_CON.equals(document.getSdtProcess())) {
                boolean isSuccessful = uploadStampedDocument(document, context);
                if (!isSuccessful) {
                    return;
                }

                logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_SDT_FIN, context);
            }

            if (logicFactory.getCommonStampingLogic().allDocumentsProcessed(documents, context)) {
                documentH.setCallbackProcess((short) 901);
                documentH.setProsesMaterai((short) 53);
                documentH.setDtmUpd(new Date());
                documentH.setUsrUpd(StringUtils.left(context.getRequestId(), 36));
                daoFactory.getDocumentDao().updateDocumentHeaderNewTran(documentH);
                    
                context.getLogger().info(String.format("Kontrak %1$s update Proses Meterai to %2$s", documentH.getRefNumber(), 53));
                return;
            }
                
            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(ERR_LOC_FINAL_VAL);
            errorDetailBean.setErrorLocationDetail(ERR_LOC_DETAIL_VALIDATION);
            errorDetailBean.setErrorMessage("Unreachable process");
            logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountPajakku(documentH, null, errorDetailBean, context);
		}
		
	}

	private boolean uploadStampedDocument(TrDocumentD document, Context context) {
        StampingErrorDetailBean errorDetailBean = uploadStampedDocumentToClient(document, context);
        if (StringUtils.isBlank(errorDetailBean.getErrorLocation())) {
            logicFactory.getCommonStampingLogic().updateDocumentDMeteraiProcess(document, Constants.STEP_STAMPING_SDT_FIN, context);
            return true;
        }

        // tidak insert reserved trx no karena tidak dibutuhkan lagi
        logicFactory.getCommonStampingLogic().incrementAgreementStampingErrorCountPajakku(document.getTrDocumentH(), document, errorDetailBean, context);
        return false;
    }

	private StampingErrorDetailBean uploadStampedDocumentToClient(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        if (StringUtils.isBlank(document.getTrDocumentH().getUrlUpload())) {
            return new StampingErrorDetailBean();
        }

        if ("1".equals(documentH.getIsStandardUploadUrl())) {
            return uploadStampedDocumentToClientWithStandardRequest(document, context);
        }

        if (Constants.TENANT_CODE_WOMF.equals(tenant.getTenantCode()) && !"1".equals(documentH.getIsManualUpload())) {
            return uploadStampedDocumentToWomfDms(document, context);
        }

        if (Constants.TENANT_CODE_CFI.equals(tenant.getTenantCode()) && "1".equals(documentH.getIsManualUpload())) {
            return uploadStampedDocumentToCfiDms(document, context);
        }

        context.getLogger().info(String.format("Kontrak %1$s, Dokumen %2$s, Tenant %3s belum ada mekanisme upload untuk tenant.", documentH.getRefNumber(), document.getDocumentId(), tenant.getTenantCode()));
        StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
        errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
        errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
        errorDetailBean.setErrorMessage("Unreachable process");
        return errorDetailBean;
    }

    private String getStandardUploadClientToken(MsTenant tenant) {
        MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GS_TOKEN_CLIENT_URL_UPLOAD);
        if (null == settings) {
            return null;
        }

        return settings.getSettingValue();
    }

    private StampingErrorDetailBean uploadStampedDocumentToClientWithStandardRequest(TrDocumentD document, Context context) {
        
        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        try {
            
            String clientToken = getStandardUploadClientToken(tenant);
            if (StringUtils.isBlank(clientToken)) {
                StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
                errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
                errorDetailBean.setErrorLocationDetail(StampingErrorDetail.VALIDATION.toString());
                errorDetailBean.setErrorMessage("Client token not found or empty");
                return errorDetailBean;
            }

            String url = document.getTrDocumentH().getUrlUpload();
            String base64Document = logicFactory.getCommonStampingLogic().getStampedDocument(document, context);

            Map<String, String> header = new HashMap<>();
            header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
            header.put(HttpHeaders.KEY_ACCEPT, HttpHeaders.APPLICATION_JSON);
            header.put("token", clientToken);
            Headers headers = Headers.of(header);

            ClientDocumentUploadRequest request = new ClientDocumentUploadRequest();
            request.setAudit(new AuditDataType(context.getRequestId()));
            request.setDocFile(DUMMY_BASE64_CONTENT);
            request.setDocNumber(documentH.getRefNumber());
            request.setTenantCode(tenant.getTenantCode());
            request.setDocId(document.getDocumentId());

            String jsonRequestLog = gson.toJson(request);
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to client request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequestLog));
            
            request.setDocFile(base64Document);
            String jsonRequest = gson.toJson(request);
            RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

            Request okHttpRequest = new Request.Builder()
                .headers(headers)
                .url(url)
                .post(body).build();
                
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to client response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));
            ClientDocumentUploadResponse response = gson.fromJson(jsonResponse, ClientDocumentUploadResponse.class);

            if (response.getStatus().getCode() == 0) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.UPL_CLIENT.toString());
            errorDetailBean.setErrorMessage("Failed");
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;

        } catch (Exception e) {
            context.getLogger().error(ExceptionUtils.getStackTrace(e));

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private StampingErrorDetailBean uploadStampedDocumentToCfiDms(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();
        
        // Headers
        Map<String, String> header = new HashMap<>();
        header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        header.put("Integration", logicFactory.getCommonStampingLogic().getIntegrationValue(document, context));
        Headers headers = Headers.of(header);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI request header: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), header));

        // Body
        CfiUploadDocumentRequest request = new CfiUploadDocumentRequest();
        request.setDokumenPeruri(document.getMsPeruriDocType().getDocName());
        request.setDokumenDate(Tools.formatDateToStringIn(document.getRequestDate(), "yyyy/MM/dd"));
        request.setFilename(document.getDocumentName() + ".pdf");
        request.setContent(DUMMY_BASE64_CONTENT); // Hardcoded for shorter logging
        request.setNotes(document.getTrDocumentH().getMsLov().getDescription());
        request.setDocumentId(document.getDocumentId());

        String jsonRequestLog = gson.toJson(request);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI request body: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequestLog));
        
        request.setContent(logicFactory.getCommonStampingLogic().getStampedDocument(document, context));
        String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // URL
        String url = document.getTrDocumentH().getUrlUpload();
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(url)
            .post(body)
            .build();
        
        try {
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to CFI response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));

            CfiUploadDocumentResponse response = gson.fromJson(jsonResponse, CfiUploadDocumentResponse.class);
            if ("200".equals(response.getStatusCode())) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.UPL_CLIENT.toString());
            errorDetailBean.setErrorMessage(ERR_MSG_FAIL_RESPONSE);
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;

        } catch (Exception e) {

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private StampingErrorDetailBean uploadStampedDocumentToWomfDms(TrDocumentD document, Context context) {

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();

        // Headers
        Map<String, String> header = new HashMap<>();
        header.put(HttpHeaders.KEY_CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
        Headers headers = Headers.of(header);

        // Body
        WomfUploadDocumentRequest request = prepareWomfUploadDocumentRequest(document, context);
        String jsonRequest = gson.toJson(request);
        RequestBody body = RequestBody.create(jsonRequest, MediaType.parse(HttpHeaders.APPLICATION_JSON));

        // URL
        String url = document.getTrDocumentH().getUrlUpload();
        Request okHttpRequest = new Request.Builder()
            .headers(headers)
            .url(url)
            .post(body)
            .build();

        try {
            OkHttpClient client = Tools.getUnsafeOkHttpClient(10, 60);
            Response okHttpResponse = client.newCall(okHttpRequest).execute();
            String jsonResponse = okHttpResponse.body().string();
            context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to WOMF response code: %4$s, body: %5$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), okHttpResponse.code(), jsonResponse));

            if (jsonResponse.contains("Message")) {
                jsonResponse = jsonResponse.replace("Message", "message");
            }
            jsonResponse = jsonResponse.replace("\\", "");

            if (jsonResponse.startsWith("\"")) {
                jsonResponse = jsonResponse.substring(1, jsonResponse.length() - 1);
            }
            Status status = gson.fromJson(jsonResponse, Status.class);

            if (status.getCode() == 200) {
                return new StampingErrorDetailBean();
            }

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.UPL_CLIENT.toString());
            errorDetailBean.setErrorMessage(StampingErrorDetail.FAIL_RESPONSE.toString());
            errorDetailBean.setJsonResponse(jsonResponse);
            return errorDetailBean;
            
        } catch (Exception e) {

            StampingErrorDetailBean errorDetailBean = new StampingErrorDetailBean();
            errorDetailBean.setErrorLocation(StampingErrorLocation.UPL_CON.toString());
            errorDetailBean.setErrorLocationDetail(StampingErrorDetail.EXCEPTION.toString());
            errorDetailBean.setErrorMessage(e.getLocalizedMessage());
            errorDetailBean.setException(e);
            return errorDetailBean;

        }

    }

    private WomfUploadDocumentRequest prepareWomfUploadDocumentRequest(TrDocumentD document, Context context) {

        WomfUploadDocumentRequestBean documentBean = new WomfUploadDocumentRequestBean();
        documentBean.setDocTypeTc(document.getMsDocTemplate().getDocTemplateCode());
        documentBean.setDisplayName(document.getMsDocTemplate().getDocTemplateName());
        documentBean.setContent(DUMMY_BASE64_CONTENT);
        documentBean.setFileName("DOC_" + document.getDocumentId() + ".pdf");

        List<WomfUploadDocumentRequestBean> documents = new ArrayList<>();
        documents.add(documentBean);

        WomfUploadDocumentRequest request = new WomfUploadDocumentRequest();
        request.setRefNo(document.getTrDocumentH().getRefNumber());
        request.setDocumentObjs(documents);

        MsTenant tenant = document.getMsTenant();
        TrDocumentH documentH = document.getTrDocumentH();
        String jsonRequest = gson.toJson(request);
        context.getLogger().info(String.format("Tenant %1$s, Kontrak %2$s, Dokumen %3$s, Upload stamped document to WOMF request: %4$s", tenant.getTenantCode(), documentH.getRefNumber(), document.getDocumentId(), jsonRequest));

        String base64Document = logicFactory.getCommonStampingLogic().getStampedDocument(document, context);
        request.getDocumentObjs().get(0).setContent(base64Document);
        return request;
    }
}
