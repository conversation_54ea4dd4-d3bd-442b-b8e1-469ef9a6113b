package com.adins.esignhubjob.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDRestore;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentHStampdutyError;

public interface DocumentDao {

    // tr_document_h
    void updateDocumentHeader(TrDocumentH docH);
    void updateDocumentHeaderNewTran(TrDocumentH documentH);
    void updateNativeStringDocumentHSigningProcessNewTrx(TrDocumentH documentH, String signingProcess, String usrUpd);

    TrDocumentH getDocumentHeaderByRefNumberNewTrx(String refNumber);
    TrDocumentH getDocumentHeaderByRefNoAndTenantCodeNewTran(String refNo, String tenantCode);
    TrDocumentH getDocumentHeaderByIdDocumentH(long idDocumentH);
    TrDocumentH getDocumentHeaderByIdDocumentHNewTrx(long idDocumentH);

    List<TrDocumentH> getDocumentWithMaxRetry(Short maxRetry);
	List<Map<String, Object>> getDocumentInformationWithMaxRetry(Short maxRetry, Short[] prosesMaterai);

    List<Map<String, Object>> getIdDocumentHs(Short[] prosesMaterai, int maxErrorCount, int maxRetryCount, int limit);
    List<Map<String, Object>> getRetryStampFromUplodaVidaIdDocumentHs(int limit);


    List<String> getExpiredUnfinishDocumentNewTrx(); // returns list of ref number

    // tr_document_d
    void updateDocumentDetail(TrDocumentD docD);
    void updateDocumentDNewTran(TrDocumentD documentD);

    TrDocumentD getDocumentDById(BigInteger idDocumentD);
    TrDocumentD getUserLatestSignRequest(long idMsUser);

    List<TrDocumentD> getListDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId);
    List<TrDocumentD> getListSignedDocumetDtoDeleteBaseDocument(Date range);
    List<TrDocumentD> getListUnsignedDocumentDtoDeleteBaseDocument(Date range);
    List<TrDocumentD> getListSignedDocumentDToDeleteSignedDocument(Date range);
    List<TrDocumentD> getListSignedAndStampedDocumentDInTenant(long idMsTenant, Date range);
    List<TrDocumentD> getListSignedAndNotstartedStampDocumentDInTenant(long idMsTenant, Date range);
    List<TrDocumentD> getListNoSignAndNotstartedStampDocumentDInTenant(long idMsTenant, Date range);
    List<TrDocumentD> getListSignedAndNoStampDocumentDInTenant(long idMsTenant, Date range);
    List<TrDocumentD> getListIncompleteSignAndStampDocumentDInTenant(long idMsTenant, Date range);

    // tr_document_d_sign
    void updateDocumentDSignNewTran(TrDocumentDSign documentDSign);

    TrDocumentDSign getLatestSignedDocumentDSign(long idMsUser);
    TrDocumentDSign getDocumentDSignById(BigInteger idDocumentDSign);
    TrDocumentDSign getUnsignedAutosignDocumentDSignNewTran(TrDocumentD document);

    List<TrDocumentDSign> getUnsignedListDocumentDSignNewTran(TrDocumentD document, AmMsuser user);
    List<TrDocumentDSign> getUnsignedListDocumentDSignNewTran(List<Long> idDocumentDs, AmMsuser user);

    Short countSignedDocumentNewTran(TrDocumentD document);
    Short countUnsignedNonAutosignDocumentDSignNewTran(TrDocumentD document);
    Short countUnsignedAutosignDocumentDSignNewTran(TrDocumentD document);

    // tr_document_d_stampduty
    void updateDocumentDStampdutyNewTrx(TrDocumentDStampduty documentDStampduty);

    List<TrDocumentDStampduty> getNotStampedDocumentDStampdutiesNewTrx(TrDocumentD document);

    BigInteger countNotStampedDocumentDStampdutiesNewTrx(TrDocumentD document);

    // tr_document_h_stampduty_error
    void insertDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError stampdutyError);
    void updateDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError stampdutyError);

    TrDocumentHStampdutyError getDocumentHStampdutyErrorNewTran(Long idDocumentH, Long idDocumentD);
    List<TrDocumentHStampdutyError> getDocumentHStampdutyErrorsNewTran(TrDocumentH documentH);

    void updateDocumentDRestoreNewTran(TrDocumentDRestore documentDRestore);
    List<TrDocumentDRestore> getExpiredDocumentRestores(Date expiredDate);

    List<TrDocumentD> getListCompletedStampDocumentDInTenant(long idMsTenant, Date range, BigInteger limitData);

    List<TrDocumentD> getListCompletedSignNoStampDocumentDInTenant(long idMsTenant, Date range, BigInteger limitData);

    // tr_verify_document_komdigi
    List<TrDocumentD> getDocumentsToVerifyKomdigiByTenantAndSessionNewTrx(long idTenant, Date startHour, Date endHour);

}
