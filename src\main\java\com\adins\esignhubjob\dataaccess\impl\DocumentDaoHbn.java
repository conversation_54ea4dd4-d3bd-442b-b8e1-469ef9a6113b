package com.adins.esignhubjob.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.constants.AmGlobalKey;
import com.adins.constants.Constants;
import com.adins.esignhubjob.dataaccess.BaseDaoHbn;
import com.adins.esignhubjob.dataaccess.api.DocumentDao;
import com.adins.esignhubjob.model.table.AmMsuser;
import com.adins.esignhubjob.model.table.MsTenant;
import com.adins.esignhubjob.model.table.TrDocumentD;
import com.adins.esignhubjob.model.table.TrDocumentDRestore;
import com.adins.esignhubjob.model.table.TrDocumentDSign;
import com.adins.esignhubjob.model.table.TrDocumentDStampduty;
import com.adins.esignhubjob.model.table.TrDocumentH;
import com.adins.esignhubjob.model.table.TrDocumentHStampdutyError;
import com.adins.util.Tools;

@Component
@Transactional
public class DocumentDaoHbn extends BaseDaoHbn implements DocumentDao {

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentH getDocumentHeaderByRefNumberNewTrx(String refNumber) {
		return this.managerDAO.selectOne(
				"from TrDocumentH th "
						+ "where th.refNumber = :refNumber ",
				new Object[][] { { "refNumber", StringUtils.upperCase(refNumber) } });
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHeader(TrDocumentH docH) {
		docH.setUsrUpd(Tools.maskData(docH.getUsrUpd()));
		managerDAO.update(docH);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@SuppressWarnings("unchecked")
	public List<TrDocumentD> getListDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId) {
		Object[][] queryParams = { { TrDocumentH.ID_DOCUMENT_H_HBM, documentHeaderId } };

		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
						+ "join fetch docD.trDocumentH docH "
						+ "join fetch docH.msOffice mo "
						+ "join fetch docH.msTenant dhmt "
						+ "left join fetch docH.msBusinessLine mbl "
						+ "join fetch docH.msLov ml "
						+ "join fetch docD.msTenant mt "
						+ "join fetch docD.msVendor mv "
						+ "left join fetch docD.msDocTemplate mdt "
						+ "left join fetch docD.msPeruriDocType mpdt "
						+ "left join fetch docD.msLovIdType mlid "
						+ "where docH.idDocumentH = :idDocumentH ",
				queryParams)
				.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentH getDocumentHeaderByRefNoAndTenantCodeNewTran(String refNo, String tenantCode) {
		Object[][] queryParams = {
				{ "refNumber", refNo },
				{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }
		};

		return this.managerDAO.selectOne(
				"from TrDocumentH docH "
						+ "join fetch docH.msTenant t "
						+ "where docH.refNumber = :refNumber and t.tenantCode = :tenantCode ",
				queryParams);
	}

	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentDSign> getUnsignedListDocumentDSignNewTran(TrDocumentD document, AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put("documentD", document);
		params.put("user", user);

		return (List<TrDocumentDSign>) managerDAO.list(
				"from TrDocumentDSign dds "
						+ "join fetch dds.trDocumentD dd "
						+ "join fetch dd.trDocumentH dh "
						+ "join fetch dd.msTenant mt "
						+ "join fetch dd.msVendor mv "
						+ "join fetch dds.amMsuser mu "
						+ "where dds.trDocumentD = :documentD "
						+ "and dds.amMsuser = :user "
						+ "and dds.signDate is null "
						+ "order by dds.signPage asc, dds.seqNo asc",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDSignNewTran(TrDocumentDSign documentDSign) {
		documentDSign.setUsrUpd(Tools.maskData(documentDSign.getUsrUpd()));
		managerDAO.update(documentDSign);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDNewTran(TrDocumentD documentD) {
		documentD.setUsrUpd(Tools.maskData(documentD.getUsrUpd()));
		managerDAO.update(documentD);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDRestoreNewTran(TrDocumentDRestore documentDRestore) {
		documentDRestore.setUsrUpd(Tools.maskData(documentDRestore.getUsrUpd()));
		managerDAO.update(documentDRestore);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHeaderNewTran(TrDocumentH documentH) {
		documentH.setUsrUpd(Tools.maskData(documentH.getUsrUpd()));
		managerDAO.update(documentH);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<TrDocumentD> getListSignedDocumetDtoDeleteBaseDocument(Date range) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		return (List<TrDocumentD>) managerDAO.list(
				"from TrDocumentD d "
						+ " join fetch d.trDocumentH h "
						+ " join fetch d.msLovByLovSignStatus signStat "
						+ " where d.requestDate < :range "
						+ " and (d.documentStorageStatus = '0' or d.documentStorageStatus is null) "
						+ " and signStat.code = 'CP' ",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<TrDocumentD> getListUnsignedDocumentDtoDeleteBaseDocument(Date range) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		return (List<TrDocumentD>) managerDAO.list(
				"from TrDocumentD d "
						+ " join fetch d.trDocumentH h "
						+ " join fetch d.msLovByLovSignStatus signStat "
						+ " where d.requestDate < :range "
						+ " and (d.documentStorageStatus = '0' or d.documentStorageStatus is null) "
						+ " and signStat.code = 'NS' ",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	public void updateDocumentDetail(TrDocumentD docD) {
		docD.setUsrUpd(Tools.maskData(docD.getUsrUpd()));
		managerDAO.update(docD);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<TrDocumentD> getListSignedDocumentDToDeleteSignedDocument(Date range) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		return (List<TrDocumentD>) managerDAO.list(
				"from TrDocumentD d "
						+ " join fetch d.trDocumentH h "
						+ " join fetch d.msLovByLovSignStatus signStat "
						+ " join fetch d.msVendor v "
						+ " where d.requestDate < :range "
						+ " and (d.documentStorageStatus = '1') "
						+ " and signStat.code = 'CP' and d.sdtProcess = 'SDT_FIN' ",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Short countSignedDocumentNewTran(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());

		StringBuilder query = new StringBuilder();
		query
				.append("select count(1) ")
				.append("from tr_document_d_sign ")
				.append("where id_document_d = :idDocumentD ")
				.append("and sign_date is not null ");

		BigInteger total = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == total) {
			return (short) 0;
		}

		return total.shortValue();
	}

	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentDSign> getUnsignedListDocumentDSignNewTran(List<Long> idDocumentDs, AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put("idDocumentDs", idDocumentDs);
		params.put("amMsuser", user);

		return (List<TrDocumentDSign>) managerDAO.list(
				"from TrDocumentDSign dds "
						+ "join fetch dds.trDocumentD dd "
						+ "join fetch dd.trDocumentH dh "
						+ "join fetch dd.msTenant mt "
						+ "join fetch dd.msVendor mv "
						+ "join fetch dds.amMsuser mu "
						+ "left join fetch dds.msLovByLovAutosign las "
						+ "where dd.idDocumentD in :idDocumentDs "
						+ "and dds.amMsuser = :amMsuser "
						+ "and dds.signDate is null "
						+ "order by dd.idDocumentD asc, dds.signPage asc, dds.seqNo asc",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<String> getExpiredUnfinishDocumentNewTrx() {
		StringBuilder query = new StringBuilder();
		List<String> result = new ArrayList<>();
		query.append(" select distinct ref_number from tr_document_h dh ")
				.append(" join lateral ( select sign_date,request_date from tr_document_d dd ")
				.append(" join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d  ")
				.append(" where dd.id_document_h = dh.id_document_h ")
				.append(" )detail on true ")
				.append(" join lateral ( select gs_value from am_generalsetting gs ")
				.append(" where gs_code = 'UNFINISH_DOCUMENT_EXPIRED_TIME' ")
				.append(" )gs on true ")
				.append(" where is_active = '1' ")
				.append(" and sign_date is null ")
				.append(" and datediff('day',CAST (request_date as Date),CAST (now() as Date) ) >= CAST (gs.gs_value AS INTEGER) ");

		Object[][] queryParams = {};
		List<Map<String, Object>> resultQuery = this.managerDAO.selectAllNativeString(query.toString(), queryParams);
		Iterator<Map<String, Object>> itr = resultQuery.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			result.add((String) map.get("d0"));
		}

		return result;
	}

	@Override
	public Short countUnsignedNonAutosignDocumentDSignNewTran(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());

		StringBuilder query = new StringBuilder()
				.append("select count(1) ")
				.append("from tr_document_d_sign ")
				.append("where id_document_d = :idDocumentD ")
				.append("and sign_date is null ")
				.append("and ( ")
				.append("lov_autosign is null ")
				.append("or lov_autosign = (select id_lov from ms_lov where lov_group = 'AUTOSIGN' and code = 'MANUAL')")
				.append(")");

		BigInteger total = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == total) {
			return (short) 0;
		}

		return total.shortValue();
	}

	@Override
	public Short countUnsignedAutosignDocumentDSignNewTran(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());

		StringBuilder query = new StringBuilder()
				.append("select count(1) ")
				.append("from tr_document_d_sign ")
				.append("where id_document_d = :idDocumentD ")
				.append("and sign_date is null ")
				.append("and lov_autosign = (select id_lov from ms_lov where lov_group = 'AUTOSIGN' and code = 'AUTO') ");

		BigInteger total = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == total) {
			return (short) 0;
		}

		return total.shortValue();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentDSign getUnsignedAutosignDocumentDSignNewTran(TrDocumentD document) {
		List<TrDocumentDSign> results = getUnsignedAutosignDocumentDSignListNewTran(document);
		// Return the first unsigned autosign document sign record for single signer with multiple sign locations
		return results.isEmpty() ? null : results.get(0);
	}

	@Override
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentDSign> getUnsignedAutosignDocumentDSignListNewTran(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put("document", document);
		params.put("lovGroup", "AUTOSIGN");
		params.put("code", "AUTO");

		return (List<TrDocumentDSign>) managerDAO.list(
				"from TrDocumentDSign dds "
						+ "join fetch dds.trDocumentD dd "
						+ "join fetch dd.trDocumentH dh "
						+ "join fetch dd.msTenant mt "
						+ "join fetch dd.msVendor mv "
						+ "join fetch dds.amMsuser mu "
						+ "join fetch dds.msLovByLovAutosign la "
						+ "where dds.trDocumentD = :document "
						+ "and dds.signDate is null "
						+ "and la.lovGroup = :lovGroup "
						+ "and la.code = :code "
						+ "order by dds.signPage asc, dds.seqNo asc",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	public TrDocumentH getDocumentHeaderByIdDocumentH(long idDocumentH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);

		return managerDAO.selectOne(
				"from TrDocumentH dh "
						+ "join fetch dh.msTenant mt "
						+ "where dh.idDocumentH = :idDocumentH ",
				params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentH getDocumentHeaderByIdDocumentHNewTrx(long idDocumentH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);

		return managerDAO.selectOne(
				"from TrDocumentH dh "
						+ "join fetch dh.msTenant mt "
						+ "left join fetch mt.lovSmsGateway lsg "
						+ "where dh.idDocumentH = :idDocumentH ",
				params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@SuppressWarnings("unchecked")
	public List<TrDocumentDStampduty> getNotStampedDocumentDStampdutiesNewTrx(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());

		return (List<TrDocumentDStampduty>) managerDAO.list(
				"from TrDocumentDStampduty dsdt "
						+ "join fetch dsdt.trDocumentD dd "
						+ "join fetch dd.trDocumentH dh "
						+ "left join fetch dsdt.trStampDuty sdt "
						+ "left join fetch sdt.msLov sdts "
						+ "where dd.idDocumentD = :idDocumentD "
						+ "and dsdt.stampingDate is null "
						+ "order by dsdt.signPage, dsdt.seqNo ",
				params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public BigInteger countNotStampedDocumentDStampdutiesNewTrx(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());

		StringBuilder query = new StringBuilder();
		query
				.append("select count(1) ")
				.append("from tr_document_d_stampduty ")
				.append("where id_document_d = :idDocumentD ")
				.append("and stamping_date is null ");

		return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentHStampdutyError getDocumentHStampdutyErrorNewTran(Long idDocumentH, Long idDocumentD) {

		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);
		if (null != idDocumentD) {
			params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		}

		StringBuilder query = new StringBuilder()
				.append("select id_document_h_stampduty_error ")
				.append("from tr_document_h_stampduty_error ")
				.append("where id_document_h = :idDocumentH ")
				.append((null != idDocumentD) ? "and id_document_d = :idDocumentD " : "and id_document_d is null ");

		BigInteger idDocumentHStampduty = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentHStampduty) {
			return null;
		}

		return this.managerDAO.selectOne(
				"from TrDocumentHStampdutyError dhse "
						+ " join fetch dhse.trDocumentH dh "
						+ " join fetch dh.msTenant mt "
						+ " left join fetch dhse.trDocumentD dd "
						+ " where dhse.idDocumentHStampdutyError = :idDocumentHStampdutyError ",
				new Object[][] { { TrDocumentHStampdutyError.ID_DOCUMENT_H_STAMPDUTY_ERROR_HBM,
						idDocumentHStampduty.longValue() } });
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError stampdutyError) {
		stampdutyError.setUsrCrt(Tools.maskData(stampdutyError.getUsrCrt()));
		managerDAO.insert(stampdutyError);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError stampdutyError) {
		stampdutyError.setUsrUpd(Tools.maskData(stampdutyError.getUsrUpd()));
		managerDAO.update(stampdutyError);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDStampdutyNewTrx(TrDocumentDStampduty documentDStampduty) {
		documentDStampduty.setUsrUpd(Tools.maskData(documentDStampduty.getUsrUpd()));
		managerDAO.update(documentDStampduty);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateNativeStringDocumentHSigningProcessNewTrx(TrDocumentH documentH, String signingProcess,
			String usrUpd) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, documentH.getIdDocumentH());
		params.put("signingProcess", signingProcess);
		params.put("usrUpd", Tools.maskData(usrUpd));

		StringBuilder query = new StringBuilder()
				.append("update tr_document_h ")
				.append("set signing_process = :signingProcess , ")
				.append("usr_upd = :usrUpd , ")
				.append("dtm_upd = now() ")
				.append("where id_document_h = :idDocumentH ");

		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public TrDocumentDSign getLatestSignedDocumentDSign(long idMsUser) {
		Object[][] params = new Object[][] {
				{ "idUser", idMsUser }
		};

		StringBuilder query = new StringBuilder();
		query.append("select id_document_d_sign from tr_document_d_sign ")
				.append("where id_ms_user = :idUser ")
				.append("order by sign_date desc nulls last ")
				.append("limit 1");

		BigInteger idDocumentDSign = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentDSign) {
			return null;
		}

		return getDocumentDSignById(idDocumentDSign);
	}

	@Override
	public TrDocumentDSign getDocumentDSignById(BigInteger idDocumentDSign) {
		Object[][] queryParams = {
				{ Restrictions.eq("idDocumentDSign", idDocumentDSign.longValue()) } };
		return managerDAO.selectOne(TrDocumentDSign.class, queryParams);
	}

	@Override
	public TrDocumentD getDocumentDById(BigInteger idDocumentD) {
		Object[][] queryParams = {
				{ Restrictions.eq("idDocumentD", idDocumentD.longValue()) } };
		return managerDAO.selectOne(TrDocumentD.class, queryParams);
	}

	@Override
	public TrDocumentD getUserLatestSignRequest(long idMsUser) {
		Object[][] params = new Object[][] {
				{ "idUser", idMsUser }
		};

		StringBuilder query = new StringBuilder();
		query.append("select id_document_d from tr_document_d d ")
				.append("join tr_document_d_sign sign on sign.id_document_d = d.id_document_d")
				.append("where sign.id_ms_user = :idUser ")
				.append("order by request_date desc ")
				.append("limit 1");

		BigInteger idDocumentD = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentD) {
			return null;
		}

		return getDocumentDById(idDocumentD);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrDocumentH> getDocumentWithMaxRetry(Short maxRetry) {
		Object[][] queryParams = { { "maxRetry", maxRetry } };

		return (List<TrDocumentH>) this.managerDAO.list(
				"select dh "
						+ "from TrDocumentHStampdutyError dhse "
						+ "join dhse.trDocumentH dh "
						+ "where dhse.retryCount = :maxRetry ",
				queryParams)
				.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<Map<String, Object>> getIdDocumentHs(Short[] prosesMaterai, int maxErrorCount, int maxRetryCount, int limit) {
		Map<String, Object> params = new HashMap<>();
        params.put("prosesMaterai", prosesMaterai);
        params.put("maxErrorCount", maxErrorCount);
		params.put("maxRetryCount", maxRetryCount);
        params.put("limit", limit);

		StringBuilder query = new StringBuilder();
		query
			.append("select distinct dh.id_document_h ")
			.append("from tr_document_h dh ")
			.append("join tr_document_h_stampduty_error hse on dh.id_document_h = hse.id_document_h ")
			.append("where dh.proses_materai in ( :prosesMaterai ) ")
			.append("and hse.error_count >= :maxErrorCount ")
			.append("and ( hse.retry_count is null or hse.retry_count < :maxRetryCount ) ")
			.append("order by dh.id_document_h ")
			.append("limit :limit ");

		return managerDAO.selectAllNativeString(query.toString(), params);

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrDocumentHStampdutyError> getDocumentHStampdutyErrorsNewTran(TrDocumentH documentH) {
		Map<String, Object> params = new HashMap<>();
		params.put("documentH", documentH);

		return (List<TrDocumentHStampdutyError>) managerDAO.list(
			"from TrDocumentHStampdutyError hse "
			+ "where hse.trDocumentH = :documentH ", params).get(Constants.MAP_RESULT_LIST);
	}

	@Override
	public List<Map<String, Object>> getDocumentInformationWithMaxRetry(Short maxRetry, Short[] prosesMaterai) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("prosesMaterai", prosesMaterai);
		params.put("maxRetryCount", maxRetry);
		
		StringBuilder query = new StringBuilder();
		query.append("select ")
			.append("mt.tenant_name as \"tenantName\", ")
			.append("COALESCE(tdsd.start_stamp_process, NULL) AS \"requestStampDate\", ")
			.append("th.ref_number AS \"refNumber\", ")
			.append("COALESCE(td.document_name, mst.doc_template_name) AS \"documentName\" ")
			.append("from tr_document_h th ")
			.append("join tr_document_d td on th.id_document_h = td.id_document_h ")
			.append("join tr_document_h_stampduty_error dhse on dhse.id_document_h = th.id_document_h ")
			.append("left join ms_doc_template mst on td.id_ms_doc_template = mst.id_doc_template ")
			.append("join ms_tenant mt on mt.id_ms_tenant = th.id_ms_tenant ")
			.append("join tr_document_d_stampduty tdsd on tdsd.id_document_d = td.id_document_d ")
			.append("where dhse.retry_count >= :maxRetryCount ")
			.append("and th.proses_materai in (:prosesMaterai)");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getRetryStampFromUplodaVidaIdDocumentHs(int limit) {
		Map<String, Object> params = new HashMap<>();
        params.put("limit", limit);

		StringBuilder query = new StringBuilder();
		query
			.append("select distinct dh.id_document_h ")
			.append("from tr_document_h dh ")
			.append("join tr_document_h_stampduty_error hse on dh.id_document_h = hse.id_document_h ")
			.append("where dh.proses_materai = 71 and is_active = '1' and coalesce(is_postpaid_stampduty,'') != '1' ")
			.append("order by dh.id_document_h ")
			.append("limit :limit ");

		return managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListSignedAndStampedDocumentDInTenant(long idMsTenant, Date range) {
		Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		StringBuilder query = new StringBuilder();
		query.append("with sdt as ( ")
			.append("  with d as (")
			.append("    select * from tr_document_d tdd ")
			.append("    join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h ")	
			.append("    where ((total_sign = tdd.total_signed and total_document = tdh.total_signed) or total_sign = 0) ")
			.append("    and total_materai >= total_stamping and tdd.id_ms_tenant = :idMsTenant ")
			.append("    and tdh.proses_materai in (3, 53, 323, 523, 63, 73) and document_deleted_date is null ")
			.append("  ) ")
			.append("  select d.id_document_d, max(stamping_date) as max_sdt_date from tr_document_d_stampduty tdds ")
			.append("  join d on tdds.id_document_d = d.id_document_d ")
			.append("  group by d.id_document_d ")
			.append(") ")
			.append("select tdd.id_document_d from tr_document_d tdd ")
			.append("join sdt on sdt.id_document_d = tdd.id_document_d ")
			.append("where sdt.max_sdt_date < cast( :range as date ) ");

		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		List<TrDocumentD> documentDs = new ArrayList<>();
			
		for(Map<String, Object> id : result) {
			BigInteger idDocD = (BigInteger) id.get("d0");
			TrDocumentD documentD = managerDAO.selectOne(TrDocumentD.class, idDocD.longValue());
			documentDs.add(documentD);
		}

		return documentDs;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListSignedAndNotstartedStampDocumentDInTenant(long idMsTenant, Date range) {
		Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		StringBuilder query = new StringBuilder();
		// query.append("from TrDocumentD d ")
		// 	 .append("join fetch d.trDocumentH h ")
		// 	 .append("where d.totalSign > 0 and d.totalSign = d.totalSigned and h.totalDocument = h.totalSigned and completedDate is not null and d.completedDate < :range ")
		// 	 .append("and h.prosesMaterai not in (3, 53, 323, 523, 63, 73) and documentDeletedDate is null ")
		// 	 .append("and d.totalMaterai > 0 and d.totalMaterai > d.totalStamping and d.msTenant.idMsTenant = :idMsTenant ")
		// 	 .append("order by d.completedDate, d.requestDate ");
		
		query.append("select tdd.* from tr_document_d tdd ")
			 .append("join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h ")
			 .append("where ((total_sign > 0 and total_sign = tdd.total_signed and total_document = tdh.total_signed and completed_date is not null and completed_date < cast( :range as date )) ")
			 .append(" or (total_sign = 0 and request_date < cast( :range as date ))) ")
			 .append(" and tdh.proses_materai not in (3, 53, 323, 523, 63, 73) and document_deleted_date is null ")
			 .append(" and total_materai > 0 and tdd.id_ms_tenant = :idMsTenant");

			 List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
			 List<TrDocumentD> documentDs = new ArrayList<>();
				 
			 for(Map<String, Object> id : result) {
				 BigInteger idDocD = (BigInteger) id.get("d0");
				 TrDocumentD documentD = managerDAO.selectOne(TrDocumentD.class, idDocD.longValue());
				 documentDs.add(documentD);
			 }
	 
			 return documentDs;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListIncompleteSignAndStampDocumentDInTenant(long idMsTenant, Date range) {
		Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		StringBuilder query = new StringBuilder();
		// query.append("from TrDocumentD d ")
		// 	 .append("where totalSign > 0 and totalSign >= totalSigned and completedDate is null ")
		// 	 .append("and d.requestDate < :range and msTenant.idMsTenant = :idMsTenant ")
		// 	 .append("order by d.requestDate ");

		query.append("select tdd.* from tr_document_d tdd ")
			 .append("where total_sign > 0 and total_sign >= total_signed and completed_date is null  ")
			 .append("and document_deleted_date is null ")
			 .append("and request_date < cast( :range as date ) and id_ms_tenant = :idMsTenant ");
		
			 List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
			 List<TrDocumentD> documentDs = new ArrayList<>();
				 
			 for(Map<String, Object> id : result) {
				 BigInteger idDocD = (BigInteger) id.get("d0");
				 TrDocumentD documentD = managerDAO.selectOne(TrDocumentD.class, idDocD.longValue());
				 documentDs.add(documentD);
			 }
	 
			 return documentDs;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListSignedAndNoStampDocumentDInTenant(long idMsTenant, Date range) {
		Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		StringBuilder query = new StringBuilder();
		// query.append("from TrDocumentD d ")
		// 	 .append("where totalSign > 0 and totalSign = totalSigned and completedDate is not null and totalMaterai = 0 ")
		// 	 .append("and d.completedDate < :range and msTenant.idMsTenant = :idMsTenant ");

		query.append("select tdd.* from tr_document_d tdd  ")
			 .append("where total_sign > 0 and total_sign = total_signed and completed_date is not null and total_materai = 0 ")
			 .append("and document_deleted_date is null ")
			 .append("and completed_date < cast( :range as date ) and tdd.id_ms_tenant = :idMsTenant ");

			 List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
			 List<TrDocumentD> documentDs = new ArrayList<>();
				 
			 for(Map<String, Object> id : result) {
				 BigInteger idDocD = (BigInteger) id.get("d0");
				 TrDocumentD documentD = managerDAO.selectOne(TrDocumentD.class, idDocD.longValue());
				 documentDs.add(documentD);
			 }
	 
			 return documentDs;
	}

	@Override
	public List<TrDocumentD> getListNoSignAndNotstartedStampDocumentDInTenant(long idMsTenant, Date range) {
		Map<String, Object> params = new HashMap<>();
        params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);

		StringBuilder query = new StringBuilder();
		query.append("from TrDocumentD d ")
			 .append("join fetch d.trDocumentH h ")
			 .append("where d.totalSign = 0 and d.requestDate < :range ")
			 .append("and h.prosesMaterai not in (3, 53, 323, 523, 63, 73) and documentDeletedDate is null ")
			 .append("and d.totalMaterai > 0 and d.totalMaterai > d.totalStamping and d.msTenant.idMsTenant = :idMsTenant ")
			 .append("order by d.completedDate, d.requestDate ");
		
		// query.append("select tdd.* from tr_document_d tdd ")
		// 	 .append("join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h ")
		// 	 .append("where ((total_sign > 0 and total_sign = tdd.total_signed and total_document = tdh.total_signed and completed_date is not null and completed_date < cast( :range as date )) ")
		// 	 .append(" or (total_sign = 0 and request_date < cast( :range as date ))) ")
		// 	 .append(" and tdh.proses_materai not in (3, 53, 323, 523, 63, 73) and document_deleted_date is null ")
		// 	 .append(" and total_materai > 0 and tdd.id_ms_tenant = :idMsTenant");

		return (List<TrDocumentD>) managerDAO.list(TrDocumentD.class, query.toString(), params)
				.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getDocumentsToVerifyKomdigiByTenantAndSessionNewTrx(long idMsTenant, Date startHour,
			Date endHour) {
		Map<String, Object> params = new HashMap<>();
		params.put("startHour", startHour);
		params.put("endHour", endHour);
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);

		StringBuilder nativeQuery = new StringBuilder();
		nativeQuery.append("SELECT filtered_docs.id_document_d FROM ( ")
				.append("    SELECT DISTINCT d.id_document_d ")
				.append("    FROM tr_document_d d ")
				.append("    JOIN tr_document_h dh ON dh.id_document_h = d.id_document_h ")
				.append("    JOIN tr_document_d_stampduty dsdt ON dsdt.id_document_d = d.id_document_d ")
				.append("	 WHERE dsdt.stamping_date >= :startHour ")
				.append("    AND dsdt.stamping_date <= :endHour ")
				.append("    AND d.id_ms_tenant = :idMsTenant ")
				.append("    AND d.total_stamping = d.total_materai ")
				.append("    AND d.sdt_process = 'SDT_FIN' ")
				.append("    AND NOT EXISTS ( ")
				.append("        SELECT 1 FROM tr_verify_document_komdigi v ")
				.append("        WHERE v.id_document_d = d.id_document_d ")
				.append("    ) ")
				.append(") AS filtered_docs ")
				.append("ORDER BY RANDOM() ")
				.append("LIMIT 10");

		List<Map<String, Object>> resultList = managerDAO.selectAllNativeString(nativeQuery.toString(), params);
		List<TrDocumentD> documents = new ArrayList<>();
		for (Map<String, Object> result : resultList) {
			BigInteger idDocD = (BigInteger) result.get("d0");
			TrDocumentD document = (TrDocumentD) managerDAO.selectOne(
					"FROM TrDocumentD d " +
							"JOIN FETCH d.trDocumentH h " +
							"WHERE d.idDocumentD = :idDocumentD",
					new Object[][] { { "idDocumentD", idDocD.longValue() } });
			documents.add(document);
		}

		return documents;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListCompletedStampDocumentDInTenant(long idMsTenant, Date range, BigInteger limitData) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);
		params.put("limitData", limitData);
		params.put("successCodes", Arrays.asList(3, 53, 323, 523, 63, 73)); // Success codes for stamping

		StringBuilder query = new StringBuilder();
		query.append("select tdd.id_document_d from tr_document_d tdd ")
				.append("join tr_document_h tdh on tdh.id_document_h = tdd.id_document_h ")
				.append("join tr_document_d_stampduty tdds on tdds.id_document_d = tdd.id_document_d ")
				.append("where tdd.document_archive_date is null ")
				.append("and tdh.proses_materai in (:successCodes) ")
				.append("and tdd.id_ms_tenant = :idMsTenant ")
				.append("and tdds.stamping_date <= cast(:range as date) ")
				.append("group by tdd.id_document_d ")
				.append("limit :limitData ");

		List<Map<String, Object>> result = managerDAO.selectAllNativeString(query.toString(), params);

		List<Long> idDocDList = result.stream().map(id -> ((BigInteger) id.get("d0")).longValue())
				.collect(Collectors.toList());

		if (idDocDList.isEmpty()) {
			return new ArrayList<>();
		}

		Map<String, Object> params2 = new HashMap<>();
		params2.put("idDocumentDList", idDocDList);
		params2.put(AmGlobalKey.DB_PARAM_RANGE, range);

		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
						+ "join fetch docD.trDocumentH "
						+ "where docD.idDocumentD in (:idDocumentDList) "
						+ "order by docD.idDocumentD asc ",
				params2).get("resultList");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListCompletedSignNoStampDocumentDInTenant(long idMsTenant, Date range,
			BigInteger limitData) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		params.put(AmGlobalKey.DB_PARAM_RANGE, range);
		params.put("limitData", limitData);

		StringBuilder query = new StringBuilder();
		query.append("select tdd.id_document_d from tr_document_d tdd ")
				.append("where tdd.document_archive_date is null ")
				.append("and tdd.completed_date is not null ")
				.append("and (tdd.total_materai = 0 or tdd.total_materai is null) ")
				.append("and tdd.id_ms_tenant = :idMsTenant ")
				.append("and tdd.completed_date <= cast(:range as date) ")
				.append("limit :limitData ");

		List<Map<String, Object>> result = managerDAO.selectAllNativeString(query.toString(), params);

		List<Long> idDocDList = result.stream().map(id -> ((BigInteger) id.get("d0")).longValue())
				.collect(Collectors.toList());

		if (idDocDList.isEmpty()) {
			return new ArrayList<>();
		}

		Map<String, Object> params2 = new HashMap<>();
		params2.put("idDocumentDList", idDocDList);
		params2.put(AmGlobalKey.DB_PARAM_RANGE, range);

		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
						+ "where docD.idDocumentD in (:idDocumentDList) "
						+ "order by docD.idDocumentD asc ",
				params2).get("resultList");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentDRestore> getExpiredDocumentRestores(Date expiredDate) {
		Map<String, Object> params = new HashMap<>();
        params.put("expiredDate", expiredDate);

		StringBuilder query = new StringBuilder();
		query.append("select dr from TrDocumentDRestore dr ")
			 .append("join fetch dr.trDocumentD d ")
			 .append("where dr.restoreExpiredDate <= :expiredDate ")
			 .append("and dr.isActive = '1' ");
		
		return (List<TrDocumentDRestore>) managerDAO.list(query.toString(), params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

}
